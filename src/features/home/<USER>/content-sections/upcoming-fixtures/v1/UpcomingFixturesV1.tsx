'use client';

import React from 'react';
import { UpcomingFixturesProps } from '../types';
import { LeagueList } from './components/LeagueList';
import { DateBasedFixtures } from './components/DateBasedFixtures';

const UpcomingFixturesV1: React.FC<UpcomingFixturesProps> = ({
  className = '',
  maxItems = 8,
  showFilters = false,
  variant = 'timeline'
}) => {
  // Simple layout with independent components

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
            <span>Leagues & Fixtures</span>
          </div>

          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Football Hub
          </h2>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore leagues and discover today's exciting matches
          </p>
        </div>

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          {/* Left Sidebar - League List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <LeagueList />
            </div>
          </div>

          {/* Right Content - Date-based Fixtures */}
          <div className="lg:col-span-3">
            <DateBasedFixtures />
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-12">
          <div className="text-sm text-gray-600">
            Click on any league to explore detailed information and standings
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingFixturesV1;

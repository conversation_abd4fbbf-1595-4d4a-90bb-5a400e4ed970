'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRealTimeFixtures } from './hooks/useRealTimeFixtures';
import { ProcessedFixture, UpcomingFixturesProps } from '../types';
import { TimelineGroup } from './components/TimelineGroup';
import { FixtureCard } from './components/FixtureCard';
import { LoadingState } from './components/LoadingState';
import { ErrorState } from './components/ErrorState';
import { EmptyState } from './components/EmptyState';
import { EnhancedLeagueFilter } from './components/EnhancedLeagueFilter';
import { DateSelector } from './components/DateSelector';
import { RealTimeIndicator } from './components/LiveStatusIndicator';

const UpcomingFixturesV1: React.FC<UpcomingFixturesProps> = ({
  className = '',
  maxItems = 8,
  showFilters = false,
  variant = 'timeline'
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedLeague, setSelectedLeague] = useState<string | null>(null);
  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  const {
    fixtures,
    isLoading,
    error,
    refetch,
    lastUpdated,
    isRealTimeActive,
    toggleRealTime
  } = useRealTimeFixtures({
    selectedDate: selectedDate || undefined,
    selectedLeague: selectedLeague || undefined,
    selectedLeagueId: selectedLeagueId || undefined,
    refreshInterval: 30000, // 30 seconds
    enableRealTime: true
  });

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Categorize fixtures by date (only when no specific date is selected)
  const categorizedFixtures = useMemo(() => {
    if (selectedDate) {
      // If specific date is selected, show all fixtures for that date
      return {
        selected: fixtures.slice(0, maxItems),
        today: [],
        tomorrow: [],
        'this-week': [],
        'next-week': []
      };
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const categorized = fixtures.map(fixture => {
      if (!fixture.date) return { ...fixture, dateCategory: 'unknown' as const };

      const fixtureDate = new Date(fixture.date);
      const fixtureDateOnly = new Date(fixtureDate.getFullYear(), fixtureDate.getMonth(), fixtureDate.getDate());

      if (fixtureDateOnly.getTime() === today.getTime()) {
        return { ...fixture, dateCategory: 'today' as const };
      } else if (fixtureDateOnly.getTime() === tomorrow.getTime()) {
        return { ...fixture, dateCategory: 'tomorrow' as const };
      } else if (fixtureDateOnly.getTime() <= nextWeek.getTime()) {
        return { ...fixture, dateCategory: 'this-week' as const };
      } else {
        return { ...fixture, dateCategory: 'next-week' as const };
      }
    });

    // Group by date category
    const groups = {
      selected: [],
      today: categorized.filter(f => f.dateCategory === 'today').slice(0, maxItems),
      tomorrow: categorized.filter(f => f.dateCategory === 'tomorrow').slice(0, maxItems),
      'this-week': categorized.filter(f => f.dateCategory === 'this-week').slice(0, maxItems),
      'next-week': categorized.filter(f => f.dateCategory === 'next-week').slice(0, maxItems)
    };

    return groups;
  }, [fixtures, maxItems, selectedDate]);

  // Loading state
  if (isLoading) {
    return <LoadingState className={className} />;
  }

  // Error state
  if (error) {
    return <ErrorState error={error} onRetry={refetch} className={className} />;
  }

  // Empty state
  const totalFixtures = Object.values(categorizedFixtures).flat().length;
  if (totalFixtures === 0) {
    return <EmptyState className={className} />;
  }

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
            <span>Live Schedule</span>
          </div>

          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Upcoming Fixtures
          </h2>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Don't miss the most exciting matches coming up
          </p>

          {/* Real-time Indicator */}
          <div className="flex justify-center mt-6">
            <RealTimeIndicator
              lastUpdated={lastUpdated}
              isActive={isRealTimeActive}
              onToggle={toggleRealTime}
            />
          </div>
        </div>

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          {/* Left Sidebar - Filters */}
          <div className="lg:col-span-1 space-y-6">
            {/* Enhanced League Filter */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <EnhancedLeagueFilter
                selectedLeague={selectedLeague}
                selectedLeagueId={selectedLeagueId}
                onLeagueSelect={(leagueName, leagueId) => {
                  setSelectedLeague(leagueName);
                  setSelectedLeagueId(leagueId);
                }}
              />
            </div>

            {/* Date Selector */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <DateSelector
                selectedDate={selectedDate}
                onDateSelect={setSelectedDate}
              />
            </div>
          </div>

          {/* Right Content - Fixtures */}
          <div className="lg:col-span-3">
            {selectedDate ? (
              /* Selected Date View */
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Fixtures for {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </h3>
                  <span className="text-sm text-gray-500">
                    {categorizedFixtures.selected.length} matches
                  </span>
                </div>

                <div className="space-y-4">
                  {categorizedFixtures.selected.map((fixture) => (
                    <FixtureCard
                      key={fixture.id}
                      fixture={fixture}
                      currentTime={currentTime}
                      variant="compact"
                    />
                  ))}
                </div>
              </div>
            ) : (
              /* Timeline View */
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 via-purple-500 to-pink-500"></div>

                {/* Timeline groups */}
                <div className="space-y-12">
                  {categorizedFixtures.today.length > 0 && (
                    <TimelineGroup
                      title="Today"
                      icon="📅"
                      color="text-red-600"
                      bgColor="bg-red-50"
                      borderColor="border-red-200"
                    >
                      {categorizedFixtures.today.map((fixture) => (
                        <FixtureCard
                          key={fixture.id}
                          fixture={fixture}
                          currentTime={currentTime}
                          variant="compact"
                        />
                      ))}
                    </TimelineGroup>
                  )}

                  {categorizedFixtures.tomorrow.length > 0 && (
                    <TimelineGroup
                      title="Tomorrow"
                      icon="➡️"
                      color="text-orange-600"
                      bgColor="bg-orange-50"
                      borderColor="border-orange-200"
                    >
                      {categorizedFixtures.tomorrow.map((fixture) => (
                        <FixtureCard
                          key={fixture.id}
                          fixture={fixture}
                          currentTime={currentTime}
                          variant="compact"
                        />
                      ))}
                    </TimelineGroup>
                  )}

                  {categorizedFixtures['this-week'].length > 0 && (
                    <TimelineGroup
                      title="This Week"
                      icon="📆"
                      color="text-blue-600"
                      bgColor="bg-blue-50"
                      borderColor="border-blue-200"
                    >
                      {categorizedFixtures['this-week'].map((fixture) => (
                        <FixtureCard
                          key={fixture.id}
                          fixture={fixture}
                          currentTime={currentTime}
                          variant="compact"
                        />
                      ))}
                    </TimelineGroup>
                  )}

                  {categorizedFixtures['next-week'].length > 0 && (
                    <TimelineGroup
                      title="Next Week"
                      icon="🗓️"
                      color="text-purple-600"
                      bgColor="bg-purple-50"
                      borderColor="border-purple-200"
                    >
                      {categorizedFixtures['next-week'].map((fixture) => (
                        <FixtureCard
                          key={fixture.id}
                          fixture={fixture}
                          currentTime={currentTime}
                          variant="compact"
                        />
                      ))}
                    </TimelineGroup>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-12">
          <button
            onClick={refetch}
            className="inline-flex items-center space-x-2 bg-white text-gray-700 px-6 py-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 shadow-sm"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Refresh Schedule</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default UpcomingFixturesV1;

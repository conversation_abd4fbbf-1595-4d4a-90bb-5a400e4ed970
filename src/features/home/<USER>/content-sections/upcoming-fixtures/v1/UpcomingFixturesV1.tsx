'use client';

import React from 'react';
import { UpcomingFixturesProps } from '../types';
import { LeagueList } from './components/LeagueList';
import { DateBasedFixtures } from './components/DateBasedFixtures';

const UpcomingFixturesV1: React.FC<UpcomingFixturesProps> = ({
  className = '',
  maxItems = 8,
  showFilters = false,
  variant = 'timeline'
}) => {
  // Simple layout with independent components

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
            <span>Leagues & Fixtures</span>
          </div>

          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Football Hub
          </h2>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore leagues and discover today's exciting matches
          </p>
        </div>

        {/* Optimized Two Column Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-10 lg:grid-cols-1 gap-8 max-w-7xl mx-auto">
          {/* Enhanced Left Sidebar - 30% width */}
          <div className="xl:col-span-3 lg:col-span-1">
            <div className="space-y-6">
              {/* Quick Stats Card */}
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-gray-900">Live Overview</h3>
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">10</div>
                    <div className="text-sm text-gray-600">Active Leagues</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">41</div>
                    <div className="text-sm text-gray-600">Live Matches</div>
                  </div>
                </div>
              </div>

              {/* League List Card */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <LeagueList />
              </div>

              {/* Quick Actions Card */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                <div className="space-y-3">
                  <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span className="font-medium">Live Scores</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span className="font-medium">Standings</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 p-3 rounded-lg bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                    <span className="font-medium">Favorites</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Streamlined Right Content - 70% width */}
          <div className="xl:col-span-7 lg:col-span-1">
            <DateBasedFixtures />
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-12">
          <div className="text-sm text-gray-600">
            Click on any league to explore detailed information and standings
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingFixturesV1;

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useActiveLeagues, League } from '../hooks/useActiveLeagues';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';

interface LeagueListProps {
  className?: string;
}

// Helper function to create league slug
const createLeagueSlug = (name: string, season: string): string => {
  const nameSlug = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
  
  return `${nameSlug}-${season}`;
};

export const LeagueList: React.FC<LeagueListProps> = ({ className = '' }) => {
  const router = useRouter();
  const { leagues, isLoading, error, refetch } = useActiveLeagues();
  const [searchTerm, setSearchTerm] = useState('');

  // Filter leagues based on search term
  const filteredLeagues = useMemo(() => {
    if (!searchTerm) return leagues;
    
    return leagues.filter(league =>
      league.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      league.country.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [leagues, searchTerm]);

  // Group leagues by priority/popularity
  const groupedLeagues = useMemo(() => {
    const popular = ['Premier League', 'La Liga', 'Serie A', 'Bundesliga', 'Ligue 1', 'Champions League'];
    
    const popularLeagues = filteredLeagues.filter(league => 
      popular.some(p => league.name.includes(p))
    );
    
    const otherLeagues = filteredLeagues.filter(league => 
      !popular.some(p => league.name.includes(p))
    );

    return { popular: popularLeagues, other: otherLeagues };
  }, [filteredLeagues]);

  const handleLeagueClick = (league: League) => {
    const slug = createLeagueSlug(league.name, league.season);
    const leagueId = league.externalId || league.id;
    
    // Navigate to league detail page
    router.push(`/league/${slug}/${leagueId}`);
  };

  if (isLoading) {
    return (
      <div className={className}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leagues</h3>
        <div className="space-y-3">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg">
                <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-6 h-6 bg-gray-300 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leagues</h3>
        <ErrorState error={error} onRetry={refetch} />
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Leagues</h3>
        <span className="text-sm text-gray-500">{leagues.length} leagues</span>
      </div>

      {/* Search Input */}
      <div className="relative mb-4">
        <input
          type="text"
          placeholder="Search leagues..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* Popular Leagues */}
      {groupedLeagues.popular.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-600 mb-3 px-1">Popular Leagues</h4>
          <div className="space-y-2">
            {groupedLeagues.popular.map((league) => (
              <button
                key={league.id}
                onClick={() => handleLeagueClick(league)}
                className="w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 bg-white text-gray-700 hover:bg-blue-50 hover:shadow-md border border-gray-200 hover:border-blue-300 group"
              >
                <div className="relative w-10 h-10 flex-shrink-0">
                  <img
                    src={league.logo}
                    alt={league.name}
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm group-hover:text-blue-600 transition-colors">
                    {league.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {league.country} • {league.season}
                  </div>
                </div>
                <div className="text-gray-400 group-hover:text-blue-500 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Other Leagues */}
      {groupedLeagues.other.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-600 mb-3 px-1">Other Leagues</h4>
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {groupedLeagues.other.map((league) => (
              <button
                key={league.id}
                onClick={() => handleLeagueClick(league)}
                className="w-full flex items-center space-x-3 p-2.5 rounded-lg transition-all duration-200 bg-white text-gray-700 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 group"
              >
                <div className="relative w-8 h-8 flex-shrink-0">
                  <img
                    src={league.logo}
                    alt={league.name}
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm group-hover:text-blue-600 transition-colors">
                    {league.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {league.country} • {league.season}
                  </div>
                </div>
                <div className="text-gray-400 group-hover:text-blue-500 transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {filteredLeagues.length === 0 && searchTerm && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2 text-2xl">🔍</div>
          <div className="text-sm text-gray-600 mb-2">No leagues found for "{searchTerm}"</div>
          <button
            onClick={() => setSearchTerm('')}
            className="text-blue-500 text-sm hover:underline"
          >
            Clear search
          </button>
        </div>
      )}

      {/* Footer Info */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          Click on any league to view detailed information
        </div>
      </div>
    </div>
  );
};

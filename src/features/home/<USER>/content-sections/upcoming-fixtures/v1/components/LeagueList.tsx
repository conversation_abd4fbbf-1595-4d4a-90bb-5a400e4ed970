import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useActiveLeagues, League } from '../hooks/useActiveLeagues';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';

interface LeagueListProps {
  className?: string;
}

// Helper function to create league slug
const createLeagueSlug = (name: string, season: string): string => {
  const nameSlug = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();

  return `${nameSlug}-${season}`;
};

export const LeagueList: React.FC<LeagueListProps> = ({ className = '' }) => {
  const router = useRouter();
  const { leagues, isLoading, error, refetch } = useActiveLeagues();

  // Group leagues by priority/popularity
  const groupedLeagues = useMemo(() => {
    const popular = ['Premier League', 'La Liga', 'Serie A', 'Bundesliga', 'Ligue 1', 'Champions League'];

    const popularLeagues = leagues.filter(league =>
      popular.some(p => league.name.includes(p))
    );

    const otherLeagues = leagues.filter(league =>
      !popular.some(p => league.name.includes(p))
    );

    return { popular: popularLeagues, other: otherLeagues };
  }, [leagues]);

  const handleLeagueClick = (league: League) => {
    const slug = createLeagueSlug(league.name, league.season);
    const leagueId = league.externalId || league.id;

    // Navigate to league detail page
    router.push(`/league/${slug}/${leagueId}`);
  };

  if (isLoading) {
    return (
      <div className={className}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leagues</h3>
        <div className="space-y-3">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg">
                <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-6 h-6 bg-gray-300 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Leagues</h3>
        <ErrorState error={error} onRetry={refetch} />
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Leagues</h3>
        <p className="text-sm text-gray-600">Explore {leagues.length} active competitions</p>
      </div>



      {/* Popular Leagues */}
      {groupedLeagues.popular.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
            Popular Leagues
          </h4>
          <div className="space-y-2">
            {groupedLeagues.popular.map((league) => (
              <button
                key={league.id}
                onClick={() => handleLeagueClick(league)}
                className="w-full flex items-center space-x-3 p-4 rounded-xl transition-all duration-200 bg-gradient-to-r from-white to-gray-50 text-gray-700 hover:from-blue-50 hover:to-blue-100 hover:shadow-lg border border-gray-200 hover:border-blue-300 group transform hover:scale-[1.02]"
              >
                <div className="relative w-12 h-12 flex-shrink-0">
                  <img
                    src={league.logo}
                    alt={league.name}
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-semibold text-sm group-hover:text-blue-700 transition-colors">
                    {league.name}
                  </div>
                  <div className="text-xs text-gray-500 group-hover:text-blue-600">
                    {league.country} • {league.season}
                  </div>
                </div>
                <div className="text-gray-400 group-hover:text-blue-600 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Other Leagues */}
      {groupedLeagues.other.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-600 mb-3 px-1">Other Leagues</h4>
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {groupedLeagues.other.map((league) => (
              <button
                key={league.id}
                onClick={() => handleLeagueClick(league)}
                className="w-full flex items-center space-x-3 p-2.5 rounded-lg transition-all duration-200 bg-white text-gray-700 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 group"
              >
                <div className="relative w-8 h-8 flex-shrink-0">
                  <img
                    src={league.logo}
                    alt={league.name}
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm group-hover:text-blue-600 transition-colors">
                    {league.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {league.country} • {league.season}
                  </div>
                </div>
                <div className="text-gray-400 group-hover:text-blue-500 transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}



      {/* Footer Info */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          Click on any league to view detailed information
        </div>
      </div>
    </div>
  );
};

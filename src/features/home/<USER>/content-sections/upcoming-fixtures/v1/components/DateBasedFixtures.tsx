import React, { useState, useMemo } from 'react';
import { usePaginatedFixtures } from '../hooks/usePaginatedFixtures';
import { FixtureCard } from './FixtureCard';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';
import { EmptyState } from './EmptyState';
import { RealTimeIndicator } from './LiveStatusIndicator';
import { EnhancedPagination } from './PaginationControls';

interface DateBasedFixturesProps {
  className?: string;
}

export const DateBasedFixtures: React.FC<DateBasedFixturesProps> = ({ className = '' }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);

  const {
    fixtures,
    isLoading,
    error,
    refetch,
    lastUpdated,
    isRealTimeActive,
    toggleRealTime,
    // Pagination
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    setPage,
    setPageSize,
    hasNextPage,
    hasPrevPage
  } = usePaginatedFixtures({
    selectedDate: selectedDate,
    refreshInterval: 30000, // 30 seconds
    enableRealTime: true,
    initialPageSize: 10
  });

  // Update current time every minute
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Quick date options
  const getQuickDateOptions = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return [
      {
        label: 'Yesterday',
        value: yesterday.toISOString().split('T')[0],
        icon: '⬅️'
      },
      {
        label: 'Today',
        value: today.toISOString().split('T')[0],
        icon: '📅'
      },
      {
        label: 'Tomorrow',
        value: tomorrow.toISOString().split('T')[0],
        icon: '➡️'
      }
    ];
  };

  const quickDateOptions = getQuickDateOptions();

  // Group fixtures by competition
  const groupedFixtures = useMemo(() => {
    const groups: { [key: string]: typeof fixtures } = {};

    fixtures.forEach(fixture => {
      const competition = fixture.competition;
      if (!groups[competition]) {
        groups[competition] = [];
      }
      groups[competition].push(fixture);
    });

    // Sort competitions by number of fixtures (descending)
    const sortedGroups = Object.entries(groups)
      .sort(([, a], [, b]) => b.length - a.length)
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {} as { [key: string]: typeof fixtures });

    return sortedGroups;
  }, [fixtures]);

  const formatSelectedDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    if (dateStr === today) return 'Today';
    if (dateStr === tomorrowStr) return 'Tomorrow';
    if (dateStr === yesterdayStr) return 'Yesterday';

    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return <LoadingState className={className} />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={refetch} className={className} />;
  }

  return (
    <div className={className}>
      {/* Simplified Header */}
      <div className="mb-8">
        {/* Main Title with Live Indicator */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <h3 className="text-2xl font-bold text-gray-900">Today's Fixtures</h3>
            {isRealTimeActive && (
              <div className="flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Live</span>
              </div>
            )}
          </div>

          {/* Compact Controls */}
          <div className="flex items-center space-x-3">
            <button
              onClick={toggleRealTime}
              className={`p-2 rounded-lg transition-colors ${isRealTimeActive
                ? 'bg-green-100 text-green-700 hover:bg-green-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              title={isRealTimeActive ? 'Pause live updates' : 'Enable live updates'}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isRealTimeActive ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                )}
              </svg>
            </button>

            <button
              onClick={refetch}
              className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
              title="Refresh fixtures"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        {/* Streamlined Date Selection */}
        <div className="bg-white rounded-xl border border-gray-200 p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Quick Date Tabs */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              {quickDateOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSelectedDate(option.value)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${selectedDate === option.value
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                    }`}
                >
                  <span>{option.icon}</span>
                  <span>{option.label}</span>
                </button>
              ))}
            </div>

            {/* Custom Date + Results Summary */}
            <div className="flex items-center space-x-4">
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />

              <div className="text-sm text-gray-600 whitespace-nowrap">
                <span className="font-medium">{totalItems}</span> matches
                {totalPages > 1 && (
                  <span className="text-gray-400"> • {totalPages} pages</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fixtures Content */}
      {fixtures.length === 0 ? (
        <EmptyState
          title="No fixtures found"
          description={`No matches scheduled for ${formatSelectedDate(selectedDate)}`}
          className="py-12"
        />
      ) : (
        <div className="space-y-6">
          {/* Smart Pagination - Top (only for large datasets) */}
          {totalPages > 3 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Page <span className="font-medium">{currentPage}</span> of <span className="font-medium">{totalPages}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPage(currentPage - 1)}
                    disabled={currentPage === 1 || isLoading}
                    className={`px-3 py-1 rounded-md text-sm transition-colors ${currentPage === 1 || isLoading
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-gray-100'
                      }`}
                  >
                    ← Previous
                  </button>
                  <button
                    onClick={() => setPage(currentPage + 1)}
                    disabled={currentPage === totalPages || isLoading}
                    className={`px-3 py-1 rounded-md text-sm transition-colors ${currentPage === totalPages || isLoading
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-gray-100'
                      }`}
                  >
                    Next →
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Fixtures by Competition */}
          {Object.entries(groupedFixtures).map(([competition, competitionFixtures]) => (
            <div key={competition} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Competition Header */}
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                    <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                    <span>{competition}</span>
                  </h4>
                  <span className="text-sm text-gray-600 bg-white px-2 py-1 rounded-full">
                    {competitionFixtures.length} {competitionFixtures.length === 1 ? 'match' : 'matches'}
                  </span>
                </div>
              </div>

              {/* Competition Fixtures */}
              <div className="p-6 space-y-4">
                {competitionFixtures.map((fixture) => (
                  <FixtureCard
                    key={fixture.id}
                    fixture={fixture}
                    currentTime={currentTime}
                    variant="compact"
                  />
                ))}
              </div>
            </div>
          ))}

          {/* Enhanced Pagination - Bottom (for navigation) */}
          {totalPages > 1 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                {/* Page Info */}
                <div className="text-sm text-gray-600">
                  Showing <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(currentPage * pageSize, totalItems)}</span> of{' '}
                  <span className="font-medium">{totalItems}</span> matches
                </div>

                {/* Navigation Controls */}
                <div className="flex items-center space-x-4">
                  {/* Page Size Selector (only for large datasets) */}
                  {totalItems > 50 && (
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-600">Show:</span>
                      <select
                        value={pageSize}
                        onChange={(e) => setPageSize(parseInt(e.target.value))}
                        disabled={isLoading}
                        className="border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={50}>50</option>
                      </select>
                    </div>
                  )}

                  {/* Page Navigation */}
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => setPage(1)}
                      disabled={currentPage === 1 || isLoading}
                      className={`px-3 py-2 text-sm rounded-md transition-colors ${currentPage === 1 || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                        }`}
                    >
                      First
                    </button>

                    <button
                      onClick={() => setPage(currentPage - 1)}
                      disabled={currentPage === 1 || isLoading}
                      className={`px-3 py-2 text-sm rounded-md transition-colors ${currentPage === 1 || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                        }`}
                    >
                      Previous
                    </button>

                    <span className="px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-md">
                      {currentPage} of {totalPages}
                    </span>

                    <button
                      onClick={() => setPage(currentPage + 1)}
                      disabled={currentPage === totalPages || isLoading}
                      className={`px-3 py-2 text-sm rounded-md transition-colors ${currentPage === totalPages || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                        }`}
                    >
                      Next
                    </button>

                    <button
                      onClick={() => setPage(totalPages)}
                      disabled={currentPage === totalPages || isLoading}
                      className={`px-3 py-2 text-sm rounded-md transition-colors ${currentPage === totalPages || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                        }`}
                    >
                      Last
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}


    </div>
  );
};

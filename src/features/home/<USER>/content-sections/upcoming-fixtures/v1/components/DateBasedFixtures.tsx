import React, { useState, useMemo } from 'react';
import { usePaginatedFixtures } from '../hooks/usePaginatedFixtures';
import { FixtureCard } from './FixtureCard';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';
import { EmptyState } from './EmptyState';
import { RealTimeIndicator } from './LiveStatusIndicator';
import { EnhancedPagination } from './PaginationControls';

interface DateBasedFixturesProps {
  className?: string;
}

export const DateBasedFixtures: React.FC<DateBasedFixturesProps> = ({ className = '' }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);

  const {
    fixtures,
    isLoading,
    error,
    refetch,
    lastUpdated,
    isRealTimeActive,
    toggleRealTime,
    // Pagination
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    setPage,
    setPageSize,
    hasNextPage,
    hasPrevPage
  } = usePaginatedFixtures({
    selectedDate: selectedDate,
    refreshInterval: 30000, // 30 seconds
    enableRealTime: true,
    initialPageSize: 20
  });

  // Update current time every minute
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Quick date options
  const getQuickDateOptions = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return [
      {
        label: 'Yesterday',
        value: yesterday.toISOString().split('T')[0],
        icon: '⬅️'
      },
      {
        label: 'Today',
        value: today.toISOString().split('T')[0],
        icon: '📅'
      },
      {
        label: 'Tomorrow',
        value: tomorrow.toISOString().split('T')[0],
        icon: '➡️'
      }
    ];
  };

  const quickDateOptions = getQuickDateOptions();

  // Group fixtures by competition
  const groupedFixtures = useMemo(() => {
    const groups: { [key: string]: typeof fixtures } = {};

    fixtures.forEach(fixture => {
      const competition = fixture.competition;
      if (!groups[competition]) {
        groups[competition] = [];
      }
      groups[competition].push(fixture);
    });

    // Sort competitions by number of fixtures (descending)
    const sortedGroups = Object.entries(groups)
      .sort(([, a], [, b]) => b.length - a.length)
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {} as { [key: string]: typeof fixtures });

    return sortedGroups;
  }, [fixtures]);

  const formatSelectedDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    if (dateStr === today) return 'Today';
    if (dateStr === tomorrowStr) return 'Tomorrow';
    if (dateStr === yesterdayStr) return 'Yesterday';

    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return <LoadingState className={className} />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={refetch} className={className} />;
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900">Fixtures</h3>
          <RealTimeIndicator
            lastUpdated={lastUpdated}
            isActive={isRealTimeActive}
            onToggle={toggleRealTime}
          />
        </div>

        {/* Quick Date Options */}
        <div className="flex flex-wrap gap-2 mb-4">
          {quickDateOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setSelectedDate(option.value)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${selectedDate === option.value
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300'
                }`}
            >
              <span>{option.icon}</span>
              <span>{option.label}</span>
            </button>
          ))}
        </div>

        {/* Custom Date Picker */}
        <div className="flex items-center space-x-3">
          <label className="text-sm font-medium text-gray-700">Custom Date:</label>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>

        {/* Selected Date Display with Pagination Info */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">
                Fixtures for {formatSelectedDate(selectedDate)}
              </h4>
              <p className="text-sm text-blue-700">
                {totalItems} total {totalItems === 1 ? 'match' : 'matches'}
                {totalPages > 1 && (
                  <span> • Page {currentPage} of {totalPages}</span>
                )}
              </p>
            </div>
            <div className="text-blue-500">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Fixtures Content */}
      {fixtures.length === 0 ? (
        <EmptyState
          title="No fixtures found"
          description={`No matches scheduled for ${formatSelectedDate(selectedDate)}`}
          className="py-12"
        />
      ) : (
        <div className="space-y-6">
          {/* Pagination Controls - Top */}
          {totalPages > 1 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={pageSize}
                onPageChange={setPage}
                isLoading={isLoading}
                currentPageSize={pageSize}
                onPageSizeChange={setPageSize}
                pageSizeOptions={[10, 20, 50]}
              />
            </div>
          )}

          {/* Fixtures by Competition */}
          {Object.entries(groupedFixtures).map(([competition, competitionFixtures]) => (
            <div key={competition} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Competition Header */}
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                    <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                    <span>{competition}</span>
                  </h4>
                  <span className="text-sm text-gray-600 bg-white px-2 py-1 rounded-full">
                    {competitionFixtures.length} {competitionFixtures.length === 1 ? 'match' : 'matches'}
                  </span>
                </div>
              </div>

              {/* Competition Fixtures */}
              <div className="p-6 space-y-4">
                {competitionFixtures.map((fixture) => (
                  <FixtureCard
                    key={fixture.id}
                    fixture={fixture}
                    currentTime={currentTime}
                    variant="compact"
                  />
                ))}
              </div>
            </div>
          ))}

          {/* Pagination Controls - Bottom */}
          {totalPages > 1 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={pageSize}
                onPageChange={setPage}
                isLoading={isLoading}
                currentPageSize={pageSize}
                onPageSizeChange={setPageSize}
                pageSizeOptions={[10, 20, 50]}
              />
            </div>
          )}
        </div>
      )}

      {/* Footer */}
      <div className="mt-8 text-center">
        <button
          onClick={refetch}
          className="inline-flex items-center space-x-2 bg-white text-gray-700 px-6 py-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 shadow-sm"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span>Refresh Fixtures</span>
        </button>
      </div>
    </div>
  );
};
